"use client"

import React, { ReactNode, useEffect, useState } from 'react';
import { SettingsProvider } from './settings-context';
import { ThemeBridge } from './theme-bridge';
import { getGlobalSettingsManager } from './global-settings';
import { SettingsManager } from './settings-manager';
import { AutoSaveProvider } from '../auto-save/auto-save-provider';

interface ClientSettingsWrapperProps {
  children: ReactNode;
}

/**
 * ✅ Client-side Settings Wrapper
 * Handles SettingsManager instantiation on the client side
 * Provides SettingsProvider and ThemeBridge to the app
 */
export const ClientSettingsWrapper: React.FC<ClientSettingsWrapperProps> = ({ children }) => {
  const [settingsManager, setSettingsManager] = useState<SettingsManager | null>(null);

  useEffect(() => {
    // Initialize SettingsManager on the client side only
    const manager = getGlobalSettingsManager();
    setSettingsManager(manager);
  }, []);

  // ✅ Don't render children until SettingsManager is initialized
  // This prevents useSettings hooks from being called without a provider
  if (!settingsManager) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing settings...</p>
        </div>
      </div>
    );
  }

  return (
    <SettingsProvider settingsManager={settingsManager}>
      <ThemeBridge />
      <AutoSaveProvider>
        {children}
      </AutoSaveProvider>
    </SettingsProvider>
  );
};
