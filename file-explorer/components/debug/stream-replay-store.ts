// components/debug/stream-replay-store.ts

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

export interface StreamChunk {
  delta: string
  timestamp: number
  originalDelay?: number // Time between this chunk and the previous one
}

export interface StreamSession {
  id: string
  agentId: string
  agentName: string
  timestamp: number
  model: string
  chunks: StreamChunk[]
  tokens: number
  cost: number
  duration: number // Total duration of the stream
  provider: string
  finishReason: string
}

export interface ReplayState {
  status: 'stopped' | 'playing' | 'paused' | 'completed'
  playbackSpeed: number
  currentChunkIndex: number
}

interface StreamReplayStore {
  // State
  sessions: StreamSession[]
  selectedSessionId: string | null
  replayState: ReplayState
  maxSessions: number

  // Actions
  addSession: (session: Omit<StreamSession, 'id'>) => void
  selectSession: (sessionId: string) => void
  clearSessions: () => void
  removeSession: (sessionId: string) => void
  
  // Replay controls
  startReplay: () => void
  pauseReplay: () => void
  stopReplay: () => void
  restartReplay: () => void
  skipToEnd: () => void
  setPlaybackSpeed: (speed: number) => void
  setCurrentChunkIndex: (index: number) => void
}

export const useStreamReplayStore = create<StreamReplayStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    sessions: [],
    selectedSessionId: null,
    replayState: {
      status: 'stopped',
      playbackSpeed: 1.0,
      currentChunkIndex: 0
    },
    maxSessions: 10, // Keep last 10 sessions

    // Session management
    addSession: (sessionData) => {
      const session: StreamSession = {
        ...sessionData,
        id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }

      set((state) => {
        const newSessions = [session, ...state.sessions]
        
        // Keep only the last maxSessions
        if (newSessions.length > state.maxSessions) {
          newSessions.splice(state.maxSessions)
        }

        return {
          sessions: newSessions,
          // Auto-select the new session if none is selected
          selectedSessionId: state.selectedSessionId || session.id
        }
      })

      console.log(`StreamReplayStore: Added session for ${sessionData.agentName} (${sessionData.chunks.length} chunks)`)
    },

    selectSession: (sessionId) => {
      set((state) => ({
        selectedSessionId: sessionId,
        replayState: {
          ...state.replayState,
          status: 'stopped',
          currentChunkIndex: 0
        }
      }))
    },

    clearSessions: () => {
      set({
        sessions: [],
        selectedSessionId: null,
        replayState: {
          status: 'stopped',
          playbackSpeed: 1.0,
          currentChunkIndex: 0
        }
      })
      console.log('StreamReplayStore: All sessions cleared')
    },

    removeSession: (sessionId) => {
      set((state) => {
        const newSessions = state.sessions.filter(s => s.id !== sessionId)
        const newSelectedId = state.selectedSessionId === sessionId 
          ? (newSessions.length > 0 ? newSessions[0].id : null)
          : state.selectedSessionId

        return {
          sessions: newSessions,
          selectedSessionId: newSelectedId,
          replayState: state.selectedSessionId === sessionId 
            ? { status: 'stopped', playbackSpeed: 1.0, currentChunkIndex: 0 }
            : state.replayState
        }
      })
    },

    // Replay controls
    startReplay: () => {
      set((state) => ({
        replayState: {
          ...state.replayState,
          status: 'playing'
        }
      }))
    },

    pauseReplay: () => {
      set((state) => ({
        replayState: {
          ...state.replayState,
          status: 'paused'
        }
      }))
    },

    stopReplay: () => {
      set((state) => ({
        replayState: {
          ...state.replayState,
          status: 'stopped',
          currentChunkIndex: 0
        }
      }))
    },

    restartReplay: () => {
      set((state) => ({
        replayState: {
          ...state.replayState,
          status: 'playing',
          currentChunkIndex: 0
        }
      }))
    },

    skipToEnd: () => {
      const { selectedSessionId, sessions } = get()
      const selectedSession = sessions.find(s => s.id === selectedSessionId)
      
      if (selectedSession) {
        set((state) => ({
          replayState: {
            ...state.replayState,
            status: 'completed',
            currentChunkIndex: selectedSession.chunks.length
          }
        }))
      }
    },

    setPlaybackSpeed: (speed) => {
      set((state) => ({
        replayState: {
          ...state.replayState,
          playbackSpeed: Math.max(0.25, Math.min(4, speed))
        }
      }))
    },

    setCurrentChunkIndex: (index) => {
      set((state) => ({
        replayState: {
          ...state.replayState,
          currentChunkIndex: index
        }
      }))
    }
  }))
)

// Helper function to create a stream session from LLM response data
export function createStreamSession(
  agentId: string,
  agentName: string,
  model: string,
  provider: string,
  chunks: Array<{ delta: string; timestamp: number }>,
  tokens: number,
  cost: number,
  finishReason: string
): Omit<StreamSession, 'id'> {
  // Calculate original delays between chunks
  const processedChunks: StreamChunk[] = chunks.map((chunk, index) => ({
    delta: chunk.delta,
    timestamp: chunk.timestamp,
    originalDelay: index > 0 ? chunk.timestamp - chunks[index - 1].timestamp : 0
  }))

  const duration = chunks.length > 0 
    ? chunks[chunks.length - 1].timestamp - chunks[0].timestamp 
    : 0

  return {
    agentId,
    agentName,
    timestamp: Date.now(),
    model,
    chunks: processedChunks,
    tokens,
    cost,
    duration,
    provider,
    finishReason
  }
}

// Global instance for easy access from LLM service
let globalStreamReplayStore: ReturnType<typeof useStreamReplayStore.getState> | null = null

export function getStreamReplayStore() {
  if (!globalStreamReplayStore) {
    globalStreamReplayStore = useStreamReplayStore.getState()
  }
  return globalStreamReplayStore
}

// Development mode only - record stream sessions
export function recordStreamSession(
  agentId: string,
  agentName: string,
  model: string,
  provider: string,
  chunks: Array<{ delta: string; timestamp: number }>,
  tokens: number,
  cost: number,
  finishReason: string
): void {
  // Only record in development mode
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  try {
    const store = getStreamReplayStore()
    const session = createStreamSession(
      agentId,
      agentName,
      model,
      provider,
      chunks,
      tokens,
      cost,
      finishReason
    )
    
    store.addSession(session)
    console.log(`🎬 StreamReplayStore: Recorded session for ${agentName} with ${chunks.length} chunks`)
  } catch (error) {
    console.warn('StreamReplayStore: Failed to record session:', error)
  }
}
