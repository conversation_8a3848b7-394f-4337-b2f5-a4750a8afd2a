// components/agents/model-registry-service.ts
import { LL<PERSON>rovider, getProviderConfig } from './llm-provider-registry';
import { OPENAI_MODEL_METADATA, OpenAIModelMetadata } from './openai-models';

export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  pricing?: {
    input: number;
    output: number;
  };
  owned_by?: string;
  created?: number;
}

export interface ModelCacheEntry {
  models: ModelInfo[];
  timestamp: number;
  ttl: number;
}

export class ModelRegistryService {
  private static instance: ModelRegistryService;
  private modelCache: Map<LLMProvider, ModelCacheEntry> = new Map();
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30 minutes
  private readonly MAX_RETRIES = 3;

  private constructor() {}

  public static getInstance(): ModelRegistryService {
    if (!ModelRegistryService.instance) {
      ModelRegistryService.instance = new ModelRegistryService();
    }
    return ModelRegistryService.instance;
  }

  /**
   * Fetch models for a specific provider with caching
   */
  public async fetchModels(provider: LLMProvider, apiKey: string, forceRefresh = false): Promise<string[]> {
    try {
      // Check cache first
      if (!forceRefresh && this.isCacheValid(provider)) {
        const cached = this.modelCache.get(provider);
        if (cached) {
          console.log(`ModelRegistryService: Using cached models for ${provider}`);
          return cached.models.map(m => m.id);
        }
      }

      const config = getProviderConfig(provider);

      // ✅ Step 5: Validate supportsModelFetching flag
      if (!config.supportsModelFetching) {
        console.log(`ModelRegistryService: ${provider} doesn't support model fetching, using static list`);
        return this.getStaticModels(provider);
      }

      // ✅ Step 1 & 2: Verify Electron API and log the path taken
      const electronAPIAvailable = this.isElectronAPIAvailable();
      console.log(`ModelRegistryService: Electron API available for ${provider}: ${electronAPIAvailable}`);

      if (!electronAPIAvailable) {
        console.warn(`⚠️ Electron API unavailable for ${provider} – falling back to static OpenAI models`);
        console.warn(`⚠️ Expected 68+ models but showing only ${Object.keys(config.modelMap).length} static models`);
        return this.getStaticModels(provider);
      }

      // ✅ Step 3: Ensure API key is valid and passed
      if (!apiKey || apiKey.trim().length === 0) {
        console.error(`❌ No API key provided for ${provider} – cannot fetch dynamic models`);
        console.warn(`⚠️ Falling back to static models for ${provider}`);
        return this.getStaticModels(provider);
      }

      console.log(`ModelRegistryService: Fetching models for ${provider} via Electron API with key: ${apiKey.substring(0, 8)}...`);

      // ✅ Step 4: Add fail-safe logging for network failures
      try {
        // Use Electron IPC to fetch models
        const modelIds = await window.electronAPI.llm.fetchModels(provider, apiKey);

        console.log(`✅ Successfully fetched ${modelIds.length} models for ${provider} from API`);

        if (modelIds.length === 0) {
          console.warn(`⚠️ API returned 0 models for ${provider} – this may indicate an API issue`);
        }

        const models: ModelInfo[] = modelIds.map(id => this.enrichModelWithMetadata(provider, id, config));

        // Cache the results
        this.cacheModels(provider, models);

        return models.map(m => m.id);

      } catch (fetchError) {
        console.error(`❌ Failed to fetch ${provider} models from API:`, fetchError);
        console.error(`❌ Network/API error details:`, {
          provider,
          apiKeyLength: apiKey.length,
          error: fetchError instanceof Error ? fetchError.message : String(fetchError)
        });
        throw fetchError; // Re-throw to trigger outer catch block
      }

    } catch (error) {
      console.error(`ModelRegistryService: Failed to fetch models for ${provider}:`, error);

      // Return cached models if available, otherwise static models
      const cached = this.modelCache.get(provider);
      if (cached) {
        console.log(`ModelRegistryService: Using stale cache for ${provider} due to error (${cached.models.length} models)`);
        return cached.models.map(m => m.id);
      }

      console.warn(`⚠️ Final fallback: Using static models for ${provider} (${Object.keys(getProviderConfig(provider).modelMap).length} models)`);
      if (provider === 'openai') {
        console.warn(`⚠️ OpenAI static fallback active – showing only 5 models instead of 68+`);
      }
      return this.getStaticModels(provider);
    }
  }

  /**
   * Enrich a model with metadata if available (non-destructive)
   */
  private enrichModelWithMetadata(provider: LLMProvider, modelId: string, config: any): ModelInfo {
    // Base model info (always present)
    const baseModel: ModelInfo = {
      id: modelId,
      name: modelId,
      description: `${config.name} model: ${modelId}`
    };

    // Enrich OpenAI models with verified metadata (if available)
    if (provider === 'openai') {
      const metadata = OPENAI_MODEL_METADATA[modelId];
      if (metadata) {
        return {
          ...baseModel,
          name: metadata.label || modelId,
          description: metadata.description || baseModel.description,
          contextLength: metadata.contextSize,
          pricing: metadata.pricing,
          owned_by: 'openai'
        };
      }
    }

    // Return base model if no metadata available (preserves all dynamic models)
    return baseModel;
  }

  /**
   * Check if Electron API is available for model fetching
   * ✅ Step 1: Enhanced Electron API availability check with detailed logging
   */
  private isElectronAPIAvailable(): boolean {
    const hasWindow = typeof window !== 'undefined';
    const hasElectronAPI = hasWindow && !!window.electronAPI;
    const hasLLMAPI = hasElectronAPI && !!window.electronAPI.llm;
    const hasFetchModels = hasLLMAPI && typeof window.electronAPI.llm.fetchModels === 'function';

    console.log('🔍 Electron API Availability Check:', {
      hasWindow,
      hasElectronAPI,
      hasLLMAPI,
      hasFetchModels,
      isElectronEnvironment: hasWindow && !!window.electronAPI
    });

    if (!hasWindow) {
      console.warn('❌ Window object not available (SSR or non-browser environment)');
    } else if (!hasElectronAPI) {
      console.warn('❌ window.electronAPI not available (running in web browser, not Electron)');
    } else if (!hasLLMAPI) {
      console.warn('❌ window.electronAPI.llm not available (Electron preload script issue)');
    } else if (!hasFetchModels) {
      console.warn('❌ window.electronAPI.llm.fetchModels not available (API method missing)');
    }

    return hasFetchModels;
  }

  /**
   * Get static models for providers that don't support dynamic fetching
   */
  private getStaticModels(provider: LLMProvider): string[] {
    const config = getProviderConfig(provider);
    return Object.keys(config.modelMap);
  }

  /**
   * Cache models for a provider
   */
  private cacheModels(provider: LLMProvider, models: ModelInfo[]): void {
    this.modelCache.set(provider, {
      models,
      timestamp: Date.now(),
      ttl: this.CACHE_TTL
    });
    console.log(`ModelRegistryService: Cached ${models.length} models for ${provider}`);
  }

  /**
   * Check if cache is valid for a provider
   */
  private isCacheValid(provider: LLMProvider): boolean {
    const cached = this.modelCache.get(provider);
    if (!cached) return false;

    const age = Date.now() - cached.timestamp;
    return age < cached.ttl;
  }

  /**
   * Clear cache for a specific provider or all providers
   */
  public clearCache(provider?: LLMProvider): void {
    if (provider) {
      this.modelCache.delete(provider);
      console.log(`ModelRegistryService: Cleared cache for ${provider}`);
    } else {
      this.modelCache.clear();
      console.log('ModelRegistryService: Cleared all model cache');
    }
  }

  /**
   * Get cached models without fetching
   */
  public getCachedModels(provider: LLMProvider): string[] | null {
    const cached = this.modelCache.get(provider);
    if (cached && this.isCacheValid(provider)) {
      return cached.models.map(m => m.id);
    }
    return null;
  }

  /**
   * Get enriched model information for a specific model
   */
  public getModelInfo(provider: LLMProvider, modelId: string): ModelInfo | null {
    const cached = this.modelCache.get(provider);
    if (cached) {
      const model = cached.models.find(m => m.id === modelId);
      if (model) {
        return model;
      }
    }

    // If not in cache, create enriched model info on demand
    const config = getProviderConfig(provider);
    return this.enrichModelWithMetadata(provider, modelId, config);
  }

  /**
   * Get all enriched models for a provider (with metadata)
   */
  public getEnrichedModels(provider: LLMProvider): ModelInfo[] {
    const cached = this.modelCache.get(provider);
    if (cached && this.isCacheValid(provider)) {
      return cached.models;
    }
    return [];
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): Record<LLMProvider, { count: number; age: number; valid: boolean }> {
    const stats: any = {};

    for (const [provider, cache] of this.modelCache.entries()) {
      const age = Date.now() - cache.timestamp;
      stats[provider] = {
        count: cache.models.length,
        age: Math.round(age / 1000), // in seconds
        valid: age < cache.ttl
      };
    }

    return stats;
  }
}
