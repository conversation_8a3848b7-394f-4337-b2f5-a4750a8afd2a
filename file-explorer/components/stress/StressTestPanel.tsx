// components/stress/StressTestPanel.tsx

"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/hooks/use-toast'
import {
  AlertTriangle,
  Play,
  Square,
  BarChart3,
  Clock,
  Zap,
  AlertCircle,
  CheckCircle2,
  Activity,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Target,
  Users,
  Timer,
  Cpu,
  MemoryStick,
  TestTube,
  Loader2,
  Trash2
} from 'lucide-react'

import { StressTestRunner, type StressTestConfig, type StressTestResult } from "../../systems/stress/StressTestRunner"
import { CompleteAgentManager } from "@/components/agents/agent-manager-complete"
import { SettingsManager } from "@/components/settings/settings-manager"
import { getAnalyticsService } from "@/services/analytics-service"

interface StressTestPanelProps {
  testModeEnabled: boolean
  agentManager: CompleteAgentManager
}

// Available agents in the system
const AVAILABLE_AGENTS = [
  { id: 'micromanager', name: '🤖 Micromanager', type: 'orchestrator' },
  { id: 'intern', name: '1️⃣ Intern', type: 'implementation' },
  { id: 'junior', name: '2️⃣ Junior', type: 'implementation' },
  { id: 'midlevel', name: '3️⃣ MidLevel', type: 'implementation' },
  { id: 'senior', name: '4️⃣ Senior', type: 'implementation' },
  { id: 'researcher', name: '📘 Researcher', type: 'specialized' },
  { id: 'architect', name: '🏗️ Architect', type: 'specialized' },
  { id: 'designer', name: '🎨 Designer', type: 'specialized' },
  { id: 'tester', name: '🧪 Tester', type: 'specialized' }
];

export default function StressTestPanel({ testModeEnabled, agentManager }: StressTestPanelProps) {
  const { toast } = useToast();
  const [settingsManager] = useState(() => new SettingsManager());
  const [stressRunner] = useState(() => new StressTestRunner(agentManager, settingsManager));
  const [analyticsService] = useState(() => getAnalyticsService());

  // Test configuration state
  const [selectedAgents, setSelectedAgents] = useState<string[]>(['micromanager', 'senior']);
  const [taskType, setTaskType] = useState<'simple' | 'complex'>('simple');
  const [duration, setDuration] = useState(30);
  const [concurrency, setConcurrency] = useState(3);
  const [maxTasks, setMaxTasks] = useState<number | undefined>(50);

  // Test execution state
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<StressTestResult | null>(null);
  const [testHistory, setTestHistory] = useState<StressTestResult[]>([]);
  const [progress, setProgress] = useState(0);

  // System settings
  const [systemSettings, setSystemSettings] = useState(settingsManager.getSystemSettings());

  useEffect(() => {
    // Load test history from analytics service
    const history = analyticsService.getStressTestResults(10);
    setTestHistory(history);
  }, [analyticsService]);

  // Development mode check
  if (process.env.NODE_ENV === 'production') {
    return null; // Don't render in production
  }

  if (!testModeEnabled) {
    return (
      <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950 dark:border-yellow-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-700 dark:text-yellow-300">
            <AlertTriangle className="h-5 w-5" />
            Stress Testing Disabled
          </CardTitle>
          <CardDescription className="text-yellow-600 dark:text-yellow-400">
            Enable test mode in System settings to access stress testing features.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const handleAgentToggle = (agentId: string, checked: boolean) => {
    if (checked) {
      setSelectedAgents(prev => [...prev, agentId]);
    } else {
      setSelectedAgents(prev => prev.filter(id => id !== agentId));
    }
  };

  const validateConfig = (): string | null => {
    if (selectedAgents.length === 0) {
      return 'Please select at least one agent';
    }
    if (concurrency > systemSettings.maxConcurrentTasks) {
      return `Concurrency (${concurrency}) cannot exceed system limit (${systemSettings.maxConcurrentTasks})`;
    }
    if (duration <= 0) {
      return 'Duration must be greater than 0';
    }
    if (concurrency <= 0) {
      return 'Concurrency must be greater than 0';
    }
    return null;
  };

  const runStressTest = async () => {
    const validationError = validateConfig();
    if (validationError) {
      toast({
        title: "Configuration Error",
        description: validationError,
        variant: "destructive",
      });
      return;
    }

    setIsRunning(true);
    setProgress(0);
    setCurrentTest(null);

    try {
      const config: StressTestConfig = {
        agents: selectedAgents,
        taskType,
        duration,
        concurrency,
        maxTasks
      };

      console.log('🧪 Starting stress test with config:', config);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + (100 / duration), 95));
      }, 1000);

      const result = await stressRunner.runTest(config);

      clearInterval(progressInterval);
      setProgress(100);

      // Report results to analytics service
      analyticsService.reportStressTestResults(result);

      // Update UI state
      setCurrentTest(result);
      setTestHistory(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 tests

      toast({
        title: "Stress Test Completed",
        description: `Test completed with ${((result.metrics.successfulTasks / result.metrics.totalTasks) * 100).toFixed(1)}% success rate`,
        variant: "default",
      });

    } catch (error) {
      console.error('Stress test failed:', error);
      toast({
        title: "Stress Test Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    } finally {
      setIsRunning(false);
      setProgress(0);
    }
  };

  const clearResults = () => {
    setTestHistory([]);
    setCurrentTest(null);
    analyticsService.clearStressTestResults();
    toast({
      title: "Results Cleared",
      description: "All stress test results have been cleared",
      variant: "default",
    });
  };

  const formatDuration = (ms: number): string => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <TestTube className="h-6 w-6" />
            Stress Test Runner
          </h2>
          <p className="text-muted-foreground">
            Test Agent System performance under high concurrency and edge cases
          </p>
        </div>
        <Badge variant="outline" className="text-xs">
          Development Mode Only
        </Badge>
      </div>

      <Tabs defaultValue="configuration" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="configuration" className="space-y-4">
          {/* Configuration Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Test Configuration</CardTitle>
              <CardDescription>
                Configure stress test parameters and select agents to test
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Agent Selection */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Select Agents to Test</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {AVAILABLE_AGENTS.map((agent) => (
                    <div key={agent.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={agent.id}
                        checked={selectedAgents.includes(agent.id)}
                        onCheckedChange={(checked) => handleAgentToggle(agent.id, checked as boolean)}
                      />
                      <Label htmlFor={agent.id} className="text-sm cursor-pointer">
                        {agent.name}
                      </Label>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground">
                  Selected: {selectedAgents.length} agent{selectedAgents.length !== 1 ? 's' : ''}
                </p>
              </div>

              {/* Test Parameters */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="taskType">Task Type</Label>
                  <Select value={taskType} onValueChange={(value: 'simple' | 'complex') => setTaskType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="simple">Simple Tasks</SelectItem>
                      <SelectItem value="complex">Complex Tasks</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration">Duration (seconds)</Label>
                  <Input
                    id="duration"
                    type="number"
                    value={duration}
                    onChange={(e) => setDuration(Number(e.target.value))}
                    min={5}
                    max={300}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="concurrency">Concurrency</Label>
                  <Input
                    id="concurrency"
                    type="number"
                    value={concurrency}
                    onChange={(e) => setConcurrency(Number(e.target.value))}
                    min={1}
                    max={systemSettings.maxConcurrentTasks}
                  />
                  <p className="text-xs text-muted-foreground">
                    Max: {systemSettings.maxConcurrentTasks}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxTasks">Max Tasks (optional)</Label>
                  <Input
                    id="maxTasks"
                    type="number"
                    value={maxTasks || ''}
                    onChange={(e) => setMaxTasks(e.target.value ? Number(e.target.value) : undefined)}
                    min={1}
                    max={1000}
                    placeholder="Auto"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3">
                <Button
                  onClick={runStressTest}
                  disabled={isRunning || selectedAgents.length === 0}
                  className="flex items-center gap-2"
                >
                  {isRunning ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Running Test...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4" />
                      Run Stress Test
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={clearResults}
                  disabled={isRunning}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear Results
                </Button>
              </div>

              {/* Progress Bar */}
              {isRunning && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Test Progress</span>
                    <span>{progress.toFixed(0)}%</span>
                  </div>
                  <Progress value={progress} className="w-full" />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {/* Current Test Results */}
          {currentTest && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Latest Test Results
                </CardTitle>
                <CardDescription>
                  Test ID: {currentTest.testId} • Duration: {formatDuration(currentTest.duration)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-accent/30 rounded">
                    <div className="text-2xl font-bold">{currentTest.metrics.totalTasks}</div>
                    <div className="text-xs text-muted-foreground">Total Tasks</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded">
                    <div className="text-2xl font-bold text-green-600">
                      {((currentTest.metrics.successfulTasks / currentTest.metrics.totalTasks) * 100).toFixed(1)}%
                    </div>
                    <div className="text-xs text-muted-foreground">Success Rate</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded">
                    <div className="text-2xl font-bold text-blue-600">
                      {currentTest.metrics.throughput.toFixed(2)}
                    </div>
                    <div className="text-xs text-muted-foreground">Tasks/sec</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-950 rounded">
                    <div className="text-2xl font-bold text-purple-600">
                      {currentTest.metrics.averageResponseTime.toFixed(0)}ms
                    </div>
                    <div className="text-xs text-muted-foreground">Avg Response</div>
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Performance Metrics */}
                  <div className="space-y-3">
                    <h4 className="font-medium flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      Performance Metrics
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Successful Tasks:</span>
                        <span className="font-medium text-green-600">{currentTest.metrics.successfulTasks}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Failed Tasks:</span>
                        <span className="font-medium text-red-600">{currentTest.metrics.failedTasks}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Timeout Tasks:</span>
                        <span className="font-medium text-yellow-600">{currentTest.metrics.timeoutTasks}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Min Response Time:</span>
                        <span className="font-medium">{currentTest.metrics.minResponseTime.toFixed(0)}ms</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Max Response Time:</span>
                        <span className="font-medium">{currentTest.metrics.maxResponseTime.toFixed(0)}ms</span>
                      </div>
                    </div>
                  </div>

                  {/* System Metrics */}
                  <div className="space-y-3">
                    <h4 className="font-medium flex items-center gap-2">
                      <Cpu className="h-4 w-4" />
                      System Metrics
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>System Health Score:</span>
                        <span className="font-medium">{currentTest.systemMetrics.systemHealthScore.toFixed(1)}/100</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Max Concurrent Tasks:</span>
                        <span className="font-medium">{currentTest.systemMetrics.maxConcurrentTasksUsed}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Concurrency Respected:</span>
                        <span className={`font-medium ${currentTest.systemMetrics.concurrencyLimitRespected ? 'text-green-600' : 'text-red-600'}`}>
                          {currentTest.systemMetrics.concurrencyLimitRespected ? 'Yes' : 'No'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Token Usage:</span>
                        <span className="font-medium">{formatNumber(currentTest.metrics.tokenUsage.total)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Errors:</span>
                        <span className="font-medium text-red-600">{currentTest.errors.length}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Errors and Warnings */}
                {(currentTest.errors.length > 0 || currentTest.warnings.length > 0) && (
                  <>
                    <Separator className="my-4" />
                    <div className="space-y-3">
                      {currentTest.errors.length > 0 && (
                        <div>
                          <h4 className="font-medium flex items-center gap-2 text-red-600">
                            <AlertCircle className="h-4 w-4" />
                            Errors ({currentTest.errors.length})
                          </h4>
                          <ScrollArea className="h-32 mt-2">
                            <div className="space-y-1">
                              {currentTest.errors.map((error, index) => (
                                <div key={index} className="text-xs p-2 bg-red-50 dark:bg-red-950 rounded">
                                  <div className="font-medium">{error.type}: {error.message}</div>
                                  {error.taskId && <div className="text-muted-foreground">Task: {error.taskId}</div>}
                                </div>
                              ))}
                            </div>
                          </ScrollArea>
                        </div>
                      )}

                      {currentTest.warnings.length > 0 && (
                        <div>
                          <h4 className="font-medium flex items-center gap-2 text-yellow-600">
                            <AlertTriangle className="h-4 w-4" />
                            Warnings ({currentTest.warnings.length})
                          </h4>
                          <div className="space-y-1 mt-2">
                            {currentTest.warnings.map((warning, index) => (
                              <div key={index} className="text-xs p-2 bg-yellow-50 dark:bg-yellow-950 rounded">
                                {warning}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}

          {/* Test History */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Test History
              </CardTitle>
              <CardDescription>
                Recent stress test results
              </CardDescription>
            </CardHeader>
            <CardContent>
              {testHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <TestTube className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No test results yet. Run a stress test to see results here.</p>
                </div>
              ) : (
                <ScrollArea className="h-96">
                  <div className="space-y-3">
                    {testHistory.map((test) => (
                      <div key={test.testId} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium">{test.testId}</div>
                          <Badge variant="outline" className="text-xs">
                            {test.config.taskType}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-4 gap-4 text-sm">
                          <div>
                            <div className="text-muted-foreground">Success Rate</div>
                            <div className="font-medium">
                              {((test.metrics.successfulTasks / test.metrics.totalTasks) * 100).toFixed(1)}%
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Throughput</div>
                            <div className="font-medium">{test.metrics.throughput.toFixed(2)} tasks/s</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Avg Response</div>
                            <div className="font-medium">{test.metrics.averageResponseTime.toFixed(0)}ms</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Health Score</div>
                            <div className="font-medium">{test.systemMetrics.systemHealthScore.toFixed(1)}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {/* Analytics Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Stress Test Analytics
              </CardTitle>
              <CardDescription>
                Aggregated performance insights across all stress tests
              </CardDescription>
            </CardHeader>
            <CardContent>
              {(() => {
                const analytics = analyticsService.getStressTestAnalytics();

                if (analytics.totalTests === 0) {
                  return (
                    <div className="text-center py-8 text-muted-foreground">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No analytics data available. Run some stress tests to see insights here.</p>
                    </div>
                  );
                }

                return (
                  <div className="space-y-6">
                    {/* Summary Cards */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-accent/30 rounded">
                        <div className="text-2xl font-bold">{analytics.totalTests}</div>
                        <div className="text-xs text-muted-foreground">Total Tests</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded">
                        <div className="text-2xl font-bold text-green-600">
                          {analytics.averageSuccessRate.toFixed(1)}%
                        </div>
                        <div className="text-xs text-muted-foreground">Avg Success Rate</div>
                      </div>
                      <div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded">
                        <div className="text-2xl font-bold text-blue-600">
                          {analytics.averageThroughput.toFixed(2)}
                        </div>
                        <div className="text-xs text-muted-foreground">Avg Throughput</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 dark:bg-purple-950 rounded">
                        <div className="text-2xl font-bold text-purple-600">
                          {analytics.averageResponseTime.toFixed(0)}ms
                        </div>
                        <div className="text-xs text-muted-foreground">Avg Response</div>
                      </div>
                    </div>

                    <Separator />

                    {/* Trend Analysis */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <h4 className="font-medium flex items-center gap-2">
                          <Activity className="h-4 w-4" />
                          Performance Trend
                        </h4>
                        <div className="flex items-center gap-2">
                          {analytics.recentTrend === 'improving' && (
                            <>
                              <TrendingUp className="h-4 w-4 text-green-600" />
                              <span className="text-green-600 font-medium">Improving</span>
                            </>
                          )}
                          {analytics.recentTrend === 'declining' && (
                            <>
                              <TrendingDown className="h-4 w-4 text-red-600" />
                              <span className="text-red-600 font-medium">Declining</span>
                            </>
                          )}
                          {analytics.recentTrend === 'stable' && (
                            <>
                              <Activity className="h-4 w-4 text-blue-600" />
                              <span className="text-blue-600 font-medium">Stable</span>
                            </>
                          )}
                          <span className="text-muted-foreground text-sm">
                            System performance trend over recent tests
                          </span>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h4 className="font-medium flex items-center gap-2">
                          <Target className="h-4 w-4" />
                          System Health
                        </h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Average Health Score</span>
                            <span className="font-medium">{analytics.averageSystemHealth.toFixed(1)}/100</span>
                          </div>
                          <Progress value={analytics.averageSystemHealth} className="w-full" />
                        </div>
                      </div>
                    </div>

                    {/* Latest Test Summary */}
                    {analytics.lastTestResult && (
                      <>
                        <Separator />
                        <div className="space-y-3">
                          <h4 className="font-medium flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            Latest Test Summary
                          </h4>
                          <div className="p-4 bg-accent/20 rounded-lg">
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <div className="text-muted-foreground">Test ID</div>
                                <div className="font-medium">{analytics.lastTestResult.testId}</div>
                              </div>
                              <div>
                                <div className="text-muted-foreground">Duration</div>
                                <div className="font-medium">{formatDuration(analytics.lastTestResult.duration)}</div>
                              </div>
                              <div>
                                <div className="text-muted-foreground">Agents Tested</div>
                                <div className="font-medium">{analytics.lastTestResult.config.agents.length}</div>
                              </div>
                              <div>
                                <div className="text-muted-foreground">Task Type</div>
                                <div className="font-medium capitalize">{analytics.lastTestResult.config.taskType}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </>
                    )}

                    {/* Recommendations */}
                    <Separator />
                    <div className="space-y-3">
                      <h4 className="font-medium flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4" />
                        Recommendations
                      </h4>
                      <div className="space-y-2">
                        {analytics.averageSuccessRate < 90 && (
                          <div className="p-3 bg-yellow-50 dark:bg-yellow-950 rounded border border-yellow-200 dark:border-yellow-800">
                            <div className="flex items-center gap-2 text-yellow-700 dark:text-yellow-300">
                              <AlertTriangle className="h-4 w-4" />
                              <span className="font-medium">Low Success Rate</span>
                            </div>
                            <p className="text-sm text-yellow-600 dark:text-yellow-400 mt-1">
                              Consider reducing concurrency or task complexity to improve success rates.
                            </p>
                          </div>
                        )}

                        {analytics.averageResponseTime > 5000 && (
                          <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded border border-blue-200 dark:border-blue-800">
                            <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                              <Clock className="h-4 w-4" />
                              <span className="font-medium">High Response Times</span>
                            </div>
                            <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                              Response times are above optimal range. Consider optimizing agent prompts or using faster models.
                            </p>
                          </div>
                        )}

                        {analytics.recentTrend === 'declining' && (
                          <div className="p-3 bg-red-50 dark:bg-red-950 rounded border border-red-200 dark:border-red-800">
                            <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
                              <TrendingDown className="h-4 w-4" />
                              <span className="font-medium">Performance Declining</span>
                            </div>
                            <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                              System performance is trending downward. Review recent changes and consider system optimization.
                            </p>
                          </div>
                        )}

                        {analytics.averageSuccessRate >= 95 && analytics.averageResponseTime <= 3000 && (
                          <div className="p-3 bg-green-50 dark:bg-green-950 rounded border border-green-200 dark:border-green-800">
                            <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                              <CheckCircle2 className="h-4 w-4" />
                              <span className="font-medium">Excellent Performance</span>
                            </div>
                            <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                              System is performing excellently with high success rates and fast response times.
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}